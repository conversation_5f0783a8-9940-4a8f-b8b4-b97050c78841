import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";

interface IUserProfile {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
}

interface IPasswordForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

const UserAccountPage = () => {
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [changingPassword, setChangingPassword] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [profile, setProfile] = useState<IUserProfile | null>(null);

  const profileSchema = yup.object({
    email: yup.string().email("Invalid email").required("Email is required"),
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
    phone: yup.string().nullable(),
    photo: yup.string().nullable(),
    address: yup.string().nullable(),
    city: yup.string().nullable(),
    state: yup.string().nullable(),
    zip_code: yup.string().nullable(),
    country: yup.string().nullable(),
  });

  const passwordSchema = yup.object({
    current_password: yup.string().required("Current password is required"),
    new_password: yup
      .string()
      .min(6, "Password must be at least 6 characters")
      .required("New password is required"),
    confirm_password: yup
      .string()
      .oneOf([yup.ref("new_password")], "Passwords must match")
      .required("Please confirm your password"),
  });

  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    setValue,
    formState: { errors: profileErrors },
  } = useForm<IUserProfile>({
    resolver: yupResolver(profileSchema),
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    reset: resetPasswordForm,
    formState: { errors: passwordErrors },
  } = useForm<IPasswordForm>({
    resolver: yupResolver(passwordSchema),
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      const result = await sdk.getProfile();

      if (result.error) {
        console.error("Error fetching profile:", result.message);
        alert("Failed to load profile. Please try again.");
      } else {
        const profileData = result.model || result.data;
        setProfile(profileData);

        // Set form values
        setValue("email", profileData.email || "");
        setValue("first_name", profileData.first_name || "");
        setValue("last_name", profileData.last_name || "");
        setValue("phone", profileData.phone || "");
        setValue("photo", profileData.photo || "");
        setValue("address", profileData.address || "");
        setValue("city", profileData.city || "");
        setValue("state", profileData.state || "");
        setValue("zip_code", profileData.zip_code || "");
        setValue("country", profileData.country || "");
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      alert("Failed to load profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onProfileSubmit = async (data: IUserProfile) => {
    setSaving(true);
    try {
      const result = await sdk.updateProfile(data);

      if (result.error) {
        console.error("Error updating profile:", result.message);
        alert("Failed to update profile. Please try again.");
      } else {
        alert("Profile updated successfully!");
        setProfile(data);
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      alert("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const onPasswordSubmit = async (data: IPasswordForm) => {
    setChangingPassword(true);
    try {
      const result = await sdk.callRestAPI(
        {
          current_password: data.current_password,
          new_password: data.new_password,
        },
        "POST"
      );

      if (result.error) {
        console.error("Error changing password:", result.message);
        alert(
          "Failed to change password. Please check your current password and try again."
        );
      } else {
        alert("Password changed successfully!");
        resetPasswordForm();
        setShowPasswordForm(false);
      }
    } catch (error) {
      console.error("Error changing password:", error);
      alert("Failed to change password. Please try again.");
    } finally {
      setChangingPassword(false);
    }
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-2">My Account</h1>
          <p className="text-gray-300">
            Manage your profile and account settings
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Profile Information */}
          <div className="bg-white rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Profile Information</h2>

            <form
              onSubmit={handleProfileSubmit(onProfileSubmit)}
              className="space-y-4"
            >
              {/* Profile Photo */}
              {profile?.photo && (
                <div className="flex justify-center mb-4">
                  <img
                    src={profile.photo}
                    alt="Profile"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Photo URL
                </label>
                <MkdInputV2
                  type="url"
                  placeholder="https://example.com/photo.jpg"
                  {...registerProfile("photo")}
                >
                  <MkdInputV2.Field />
                </MkdInputV2>
                {profileErrors.photo && (
                  <p className="text-red-500 text-sm mt-1">
                    {profileErrors.photo.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    First Name *
                  </label>
                  <MkdInputV2
                    placeholder="Enter first name"
                    {...registerProfile("first_name")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                  {profileErrors.first_name && (
                    <p className="text-red-500 text-sm mt-1">
                      {profileErrors.first_name.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Name *
                  </label>
                  <MkdInputV2
                    placeholder="Enter last name"
                    {...registerProfile("last_name")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                  {profileErrors.last_name && (
                    <p className="text-red-500 text-sm mt-1">
                      {profileErrors.last_name.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <MkdInputV2
                  type="email"
                  placeholder="Enter email"
                  {...registerProfile("email")}
                >
                  <MkdInputV2.Field />
                </MkdInputV2>
                {profileErrors.email && (
                  <p className="text-red-500 text-sm mt-1">
                    {profileErrors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <MkdInputV2
                  type="tel"
                  placeholder="Enter phone number"
                  {...registerProfile("phone")}
                >
                  <MkdInputV2.Field />
                </MkdInputV2>
                {profileErrors.phone && (
                  <p className="text-red-500 text-sm mt-1">
                    {profileErrors.phone.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Address
                </label>
                <MkdInputV2
                  placeholder="Enter address"
                  {...registerProfile("address")}
                >
                  <MkdInputV2.Field />
                </MkdInputV2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <MkdInputV2
                    placeholder="Enter city"
                    {...registerProfile("city")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    State
                  </label>
                  <MkdInputV2
                    placeholder="Enter state"
                    {...registerProfile("state")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ZIP Code
                  </label>
                  <MkdInputV2
                    placeholder="Enter ZIP code"
                    {...registerProfile("zip_code")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Country
                </label>
                <MkdInputV2
                  placeholder="Enter country"
                  {...registerProfile("country")}
                >
                  <MkdInputV2.Field />
                </MkdInputV2>
              </div>

              <InteractiveButton
                type="submit"
                disabled={saving}
                className="w-full bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] disabled:bg-gray-400"
              >
                {saving ? "Saving..." : "Save Profile"}
              </InteractiveButton>
            </form>
          </div>

          {/* Security Settings */}
          <div className="bg-white rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Security Settings</h2>

            {!showPasswordForm ? (
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Password</h3>
                  <p className="text-gray-600 text-sm mb-3">
                    Keep your account secure by using a strong password
                  </p>
                  <InteractiveButton
                    onClick={() => setShowPasswordForm(true)}
                    className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
                  >
                    Change Password
                  </InteractiveButton>
                </div>

                <div className="p-4 bg-gray-50 rounded-lg">
                  <h3 className="font-medium mb-2">Account Status</h3>
                  <p className="text-gray-600 text-sm mb-3">
                    Your account is active and in good standing
                  </p>
                  <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                    Active
                  </span>
                </div>
              </div>
            ) : (
              <form
                onSubmit={handlePasswordSubmit(onPasswordSubmit)}
                className="space-y-4"
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Password *
                  </label>
                  <MkdInputV2
                    type="password"
                    placeholder="Enter current password"
                    {...registerPassword("current_password")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                  {passwordErrors.current_password && (
                    <p className="text-red-500 text-sm mt-1">
                      {passwordErrors.current_password.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    New Password *
                  </label>
                  <MkdInputV2
                    type="password"
                    placeholder="Enter new password"
                    {...registerPassword("new_password")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                  {passwordErrors.new_password && (
                    <p className="text-red-500 text-sm mt-1">
                      {passwordErrors.new_password.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm New Password *
                  </label>
                  <MkdInputV2
                    type="password"
                    placeholder="Confirm new password"
                    {...registerPassword("confirm_password")}
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                  {passwordErrors.confirm_password && (
                    <p className="text-red-500 text-sm mt-1">
                      {passwordErrors.confirm_password.message}
                    </p>
                  )}
                </div>

                <div className="flex gap-3">
                  <InteractiveButton
                    type="submit"
                    disabled={changingPassword}
                    className="flex-1 bg-[#e53e3e] text-white py-3 px-6 rounded-md hover:bg-[#c53030] disabled:bg-gray-400"
                  >
                    {changingPassword ? "Changing..." : "Change Password"}
                  </InteractiveButton>

                  <InteractiveButton
                    type="button"
                    onClick={() => {
                      setShowPasswordForm(false);
                      resetPasswordForm();
                    }}
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </InteractiveButton>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </UserWrapper>
  );
};

export default UserAccountPage;
