import React, { useState, useEffect } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import TopUpRequestModal from "@/components/TopUpRequestModal";
import AddNewCardModal from "@/components/AddNewCardModal";

interface IUserProfile {
  email: string;
  first_name: string;
  last_name: string;
  phone?: string | null;
  photo?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  zip_code?: string | null;
  country?: string | null;
}

interface PaymentMethod {
  id: string;
  type: "visa" | "mastercard";
  last4: string;
  expires: string;
  isDefault?: boolean;
}

interface TopUpMethod {
  id: string;
  name: string;
  icon: string;
}

interface PendingRequest {
  id: string;
  amount: string;
  method: string;
  date: string;
}

interface TopUpHistory {
  date: string;
  method: string;
  reference: string;
  amount: string;
  status: "Pending" | "Approved";
}

interface ExchangeListing {
  id: string;
  type: "selling" | "buying";
  amount: string;
  rate: string;
}

const UserAccountPage = () => {
  const { sdk } = useSDK();
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<IUserProfile | null>(null);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(
    "Credit Card (Visa ending in 4242)"
  );
  const [topUpAmount, setTopUpAmount] = useState("");
  const [showTopUpModal, setShowTopUpModal] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);

  // Mock data - in real app this would come from API
  const paymentMethods: PaymentMethod[] = [
    {
      id: "1",
      type: "visa",
      last4: "4242",
      expires: "09/2027",
      isDefault: true,
    },
    {
      id: "2",
      type: "mastercard",
      last4: "8888",
      expires: "05/2026",
    },
  ];

  const topUpMethods: TopUpMethod[] = [
    { id: "paypal", name: "PayPal", icon: "💳" },
    { id: "bank", name: "Bank Transfer", icon: "🏦" },
    { id: "credit", name: "Credit/Debit Card", icon: "💳" },
    { id: "etransfer", name: "e-Transfer", icon: "📱" },
    { id: "wire", name: "Local Wire Transfer (Jamaica)", icon: "🏛️" },
  ];

  const pendingRequests: PendingRequest[] = [
    {
      id: "1",
      amount: "$150.00 via Bank Transfer",
      method: "Bank Transfer",
      date: "Submitted on Apr 20, 2023",
    },
  ];

  const topUpHistory: TopUpHistory[] = [
    {
      date: "Apr 20, 2023",
      method: "Bank Transfer",
      reference: "BT202304201",
      amount: "$150.00",
      status: "Pending",
    },
    {
      date: "Apr 15, 2023",
      method: "PayPal",
      reference: "PP202304150",
      amount: "$200.00",
      status: "Approved",
    },
    {
      date: "Apr 05, 2023",
      method: "Credit Card",
      reference: "CC202304050",
      amount: "$150.00",
      status: "Approved",
    },
  ];

  const exchangeListings: ExchangeListing[] = [
    {
      id: "1",
      type: "selling",
      amount: "500 eBa$",
      rate: "$1.05 per eBa$",
    },
    {
      id: "2",
      type: "selling",
      amount: "1,200 eBa$",
      rate: "$1.02 per eBa$",
    },
    {
      id: "3",
      type: "buying",
      amount: "800 eBa$",
      rate: "$0.98 per eBa$",
    },
  ];

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      const result = await sdk.getProfile();

      if (result.error) {
        console.error("Error fetching profile:", result.message);
        // Set mock data for demo
        setProfile({
          email: "<EMAIL>",
          first_name: "Alex",
          last_name: "Johnson",
          phone: "+****************",
          photo:
            "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
          address: null,
          city: null,
          state: null,
          zip_code: null,
          country: "United States",
        });
      } else {
        const profileData = result.model || result.data;
        setProfile(profileData);
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      // Set mock data for demo
      setProfile({
        email: "<EMAIL>",
        first_name: "Alex",
        last_name: "Johnson",
        phone: "+****************",
        photo:
          "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        address: null,
        city: null,
        state: null,
        zip_code: null,
        country: "United States",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <UserWrapper>
        <div className="flex justify-center items-center py-12">
          <MkdLoader />
        </div>
      </UserWrapper>
    );
  }

  return (
    <UserWrapper>
      <div className="p-8 bg-[#0F2C59] min-h-screen">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">My Account</h1>
        </div>

        <div className="space-y-6">
          {/* Profile Header */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full overflow-hidden">
                <img
                  src={
                    profile?.photo ||
                    "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                  }
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">
                  {profile?.first_name} {profile?.last_name}
                </h2>
                <p className="text-gray-600">{profile?.email}</p>
                <p className="text-sm text-gray-500">
                  Member since: April 2023
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Details */}
            <div className="bg-white rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Personal Details
                </h3>
                <button className="text-[#E63946] hover:underline text-sm font-medium">
                  Edit
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Full Name
                  </label>
                  <p className="text-gray-900">
                    {profile?.first_name} {profile?.last_name}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Email Address
                  </label>
                  <p className="text-gray-900">{profile?.email}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                  <p className="text-gray-900">{profile?.phone}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Country
                  </label>
                  <p className="text-gray-900">{profile?.country}</p>
                </div>
              </div>
            </div>

            {/* Password Settings */}
            <div className="bg-white rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                Password Settings
              </h3>

              <p className="text-gray-600 text-sm mb-6">
                Password must be at least 8 characters and contain at least one
                letter and one number.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Password
                  </label>
                  <input
                    type="password"
                    value="••••••••"
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                  </label>
                  <input
                    type="password"
                    placeholder="Enter new password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    placeholder="Confirm new password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                  />
                </div>

                <InteractiveButton className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-6 py-2 rounded-md">
                  Update Password
                </InteractiveButton>
              </div>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Payment Methods
              </h3>
              <InteractiveButton
                onClick={() => setShowAddCardModal(true)}
                className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-4 py-2 rounded-md text-sm"
              >
                + Add New Card
              </InteractiveButton>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-6 bg-blue-600 rounded flex items-center justify-center">
                        {method.type === "visa" ? (
                          <span className="text-white text-xs font-bold">
                            VISA
                          </span>
                        ) : (
                          <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {method.type === "visa" ? "Visa" : "Mastercard"}{" "}
                          ending in {method.last4}
                        </p>
                        <p className="text-sm text-gray-500">
                          Expires: {method.expires}
                        </p>
                      </div>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      <svg
                        className="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>
                  </div>
                  {method.isDefault && (
                    <p className="text-sm text-gray-500">
                      Default payment method
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Prepaid Account */}
          <div className="bg-white rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Prepaid Account
              </h3>
              <InteractiveButton
                onClick={() => setShowTopUpModal(true)}
                className="bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white px-4 py-2 rounded-md text-sm"
              >
                + Submit Top-up Request
              </InteractiveButton>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Current Balance */}
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className="text-sm font-medium text-gray-700">
                    Current Balance
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">💰</span>
                  </div>
                </div>
                <p className="text-3xl font-bold text-gray-900">$350.00</p>
                <p className="text-sm text-gray-500">
                  Last topped up: Apr 15, 2023
                </p>
              </div>

              {/* Top-up Methods */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <h4 className="text-sm font-medium text-gray-700">
                    Top-up Methods
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">💳</span>
                  </div>
                </div>
                <div className="space-y-2">
                  {topUpMethods.map((method) => (
                    <div
                      key={method.id}
                      className="flex items-center space-x-2 text-sm"
                    >
                      <span>{method.icon}</span>
                      <span className="text-gray-700">{method.name}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pending Requests */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <h4 className="text-sm font-medium text-gray-700">
                    Pending Requests
                  </h4>
                  <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">ℹ</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-gray-900">1</div>
                  {pendingRequests.map((request) => (
                    <div key={request.id} className="text-sm">
                      <p className="text-gray-900">{request.amount}</p>
                      <p className="text-gray-500">{request.date}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* e-Transfer Instructions */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                e-Transfer Instructions
              </h4>
              <p className="text-sm text-gray-600 mb-1">
                Send e-transfer to: <EMAIL>
              </p>
              <p className="text-sm text-gray-600">
                Include your registered email in the message
              </p>
            </div>

            {/* Jamaica Wire Transfer Details */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Jamaica Wire Transfer Details
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">
                    Bank: National Commercial Bank Jamaica
                  </p>
                  <p className="text-gray-600">
                    Account Name: eBa Platform Ltd
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Account Number: *********</p>
                  <p className="text-gray-600">Branch: Kingston Main</p>
                  <p className="text-gray-600">Swift Code: JNCBJMKX</p>
                </div>
              </div>
            </div>

            {/* Top-up Request History */}
            <div className="mt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                Top-up Request History
              </h4>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2 text-sm font-medium text-gray-700">
                        Date
                      </th>
                      <th className="text-left py-2 text-sm font-medium text-gray-700">
                        Payment Method
                      </th>
                      <th className="text-left py-2 text-sm font-medium text-gray-700">
                        Reference
                      </th>
                      <th className="text-left py-2 text-sm font-medium text-gray-700">
                        Amount
                      </th>
                      <th className="text-left py-2 text-sm font-medium text-gray-700">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {topUpHistory.map((item, index) => (
                      <tr key={index} className="border-b border-gray-100">
                        <td className="py-3 text-sm text-gray-900">
                          {item.date}
                        </td>
                        <td className="py-3 text-sm text-gray-900">
                          {item.method}
                        </td>
                        <td className="py-3 text-sm text-gray-900">
                          {item.reference}
                        </td>
                        <td className="py-3 text-sm text-gray-900">
                          {item.amount}
                        </td>
                        <td className="py-3">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              item.status === "Approved"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }`}
                          >
                            {item.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* eBa Dollars Exchange */}
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">
              eBa Dollars Exchange
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Buy eBa Dollars */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-green-600">💰</span>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Buy eBa Dollars
                  </h4>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  Purchase eBa dollars directly from the platform using your
                  preferred payment method.
                </p>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Amount (eBa$)
                    </label>
                    <input
                      type="text"
                      placeholder="Enter amount"
                      value={topUpAmount}
                      onChange={(e) => setTopUpAmount(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method
                    </label>
                    <select
                      value={selectedPaymentMethod}
                      onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                    >
                      <option>Credit Card (Visa ending in 4242)</option>
                      <option>Credit Card (Mastercard ending in 8888)</option>
                    </select>
                  </div>

                  <InteractiveButton className="w-full bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white py-3 rounded-md">
                    Purchase eBa Dollars
                  </InteractiveButton>
                </div>
              </div>

              {/* Peer-to-Peer Exchange */}
              <div>
                <div className="flex items-center space-x-2 mb-4">
                  <span className="text-blue-600">🔄</span>
                  <h4 className="text-lg font-semibold text-gray-900">
                    Peer-to-Peer Exchange
                  </h4>
                </div>
                <p className="text-gray-600 text-sm mb-4">
                  Buy or sell eBa dollars with other users in the community.
                </p>

                <div className="flex space-x-2 mb-4">
                  <InteractiveButton className="flex-1 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white py-2 rounded-md">
                    Buy eBa$
                  </InteractiveButton>
                  <InteractiveButton className="flex-1 border border-gray-300 text-gray-700 py-2 rounded-md hover:bg-gray-50">
                    Sell eBa$
                  </InteractiveButton>
                </div>

                <div className="space-y-3">
                  {exchangeListings.map((listing) => (
                    <div
                      key={listing.id}
                      className="flex justify-between items-center p-3 border border-gray-200 rounded-lg"
                    >
                      <div>
                        <p className="font-medium text-gray-900">
                          {listing.type === "selling" ? "Selling" : "Buying"}{" "}
                          {listing.amount}
                        </p>
                        <p className="text-sm text-gray-600">
                          Rate: {listing.rate}
                        </p>
                      </div>
                      <InteractiveButton className="text-[#0F2C59] hover:underline text-sm">
                        View
                      </InteractiveButton>
                    </div>
                  ))}

                  <InteractiveButton className="w-full text-[#0F2C59] hover:underline text-sm py-2">
                    + Create New Listing
                  </InteractiveButton>
                </div>
              </div>
            </div>
          </div>

          {/* Deactivate Account */}
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Deactivate Account
            </h3>
            <p className="text-gray-600 text-sm mb-6">
              Deactivating your account will temporarily disable your profile
              and services until you log in again. All your data will be
              preserved.
            </p>
            <InteractiveButton className="bg-[#E63946] hover:bg-[#E63946]/90 text-white px-6 py-2 rounded-md">
              Deactivate Account
            </InteractiveButton>
          </div>
        </div>

        {/* Currency Converter Widget */}
        <div className="fixed bottom-4 left-4 bg-white rounded-lg shadow-lg p-4 w-64 border border-gray-200">
          <h4 className="text-sm font-semibold text-gray-900 mb-3">
            Currency Converter
          </h4>
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="50"
                defaultValue="50"
                className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm"
              />
              <span className="text-sm text-gray-600">eBa$</span>
            </div>
            <div className="flex items-center space-x-2">
              <select className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">
                <option>USD</option>
                <option>CAD</option>
                <option>JMD</option>
              </select>
              <span className="text-sm text-gray-600">= 72.50 USD</span>
            </div>
            <p className="text-xs text-gray-500">eBa$50 = 72.50 USD</p>
          </div>
        </div>

        {/* Inbox Widget */}
        <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-xs">📧</span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">Inbox 3</p>
              <div className="flex items-center space-x-1">
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <img
                    src={
                      profile?.photo ||
                      "https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                    }
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-xs text-gray-600">
                  {profile?.first_name} {profile?.last_name}
                </span>
                <svg
                  className="w-3 h-3 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top-Up Request Modal */}
      <TopUpRequestModal
        isOpen={showTopUpModal}
        onClose={() => setShowTopUpModal(false)}
        onSubmit={(data) => {
          console.log("Top-up request submitted:", data);
          // Handle the submission here
        }}
      />

      {/* Add New Card Modal */}
      <AddNewCardModal
        isOpen={showAddCardModal}
        onClose={() => setShowAddCardModal(false)}
        onSubmit={(data) => {
          console.log("New card added:", data);
          // Handle the card addition here
        }}
      />
    </UserWrapper>
  );
};

export default UserAccountPage;
