import React, { useEffect, useState, useCallback } from "react";
import { UserWrapper } from "@/components/UserWrapper";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { Link, useNavigate } from "react-router-dom";
import { useSDK } from "@/hooks/useSDK";
import { MkdLoader } from "@/components/MkdLoader";
import { PaginationBar } from "@/components/PaginationBar";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { PlusIcon, EditIcon, TrashIcon } from "@/assets/svgs";

interface IListing {
  id: number;
  name: string;
  price: string;
  status: string;
  description?: string;
  image?: string;
  category?: string;
  created_at: string;
  updated_at: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const UserListingsListPage = () => {
  const navigate = useNavigate();
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<IPagination | null>(null);
  const [statusCounts, setStatusCounts] = useState({
    all: 0,
    active: 0,
    sold: 0,
    expired: 0,
  });
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: "",
    status: "",
  });
  const [searchInput, setSearchInput] = useState("");

  const { sdk } = useSDK();

  const fetchListings = async () => {
    setLoading(true);
    try {
      console.log("Fetching user listings with filters:", filters);

      const params = {
        page: filters.page,
        limit: filters.limit,
        ...(filters.search && { search: filters.search }),
        ...(filters.status && { status: filters.status }),
        // Add user_id filter to only show current user's listings
        // user_id: currentUserId, // This would come from auth context
      };

      const result = await sdk.callRestAPI(
        {
          where: params,
        },
        "PAGINATE"
      );

      if (result.error) {
        console.error("Error fetching listings:", result.message);
        setListings([]);
        setPagination(null);
      } else {
        setListings(result.list || []);
        setPagination(result.pagination || null);

        // Calculate status counts
        const counts = { all: 0, active: 0, sold: 0, expired: 0 };
        result.list?.forEach((listing: IListing) => {
          counts.all++;
          if (listing.status === "active") counts.active++;
          else if (listing.status === "sold") counts.sold++;
          else if (listing.status === "expired") counts.expired++;
        });
        setStatusCounts(counts);
      }
    } catch (error) {
      console.error("Error fetching listings:", error);
      setListings([]);
      setPagination(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchListings();
  }, [filters.page, filters.status]);

  const handleSearch = useCallback(() => {
    setFilters((prev) => ({
      ...prev,
      search: searchInput,
      page: 1,
    }));
  }, [searchInput]);

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleStatusFilter = (status: string) => {
    setFilters((prev) => ({
      ...prev,
      status: status === "all" ? "" : status,
      page: 1,
    }));
  };

  const handleDeleteListing = async (id: number) => {
    if (!confirm("Are you sure you want to delete this listing?")) return;

    try {
      const result = await sdk.callRestAPI({ id }, "DELETE");

      if (result.error) {
        console.error("Error deleting listing:", result.message);
        alert("Failed to delete listing. Please try again.");
      } else {
        alert("Listing deleted successfully!");
        fetchListings(); // Refresh the list
      }
    } catch (error) {
      console.error("Error deleting listing:", error);
      alert("Failed to delete listing. Please try again.");
    }
  };

  const statusTabs = [
    { key: "all", label: "All", count: statusCounts.all },
    { key: "active", label: "Active", count: statusCounts.active },
    { key: "sold", label: "Sold", count: statusCounts.sold },
    { key: "expired", label: "Expired", count: statusCounts.expired },
  ];

  return (
    <UserWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">My Listings</h1>
            <p className="text-gray-300">Manage your products and services</p>
          </div>
          <Link
            to="/user/listings/add"
            className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030] flex items-center gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            Add New Listing
          </Link>
        </div>

        {/* Status Tabs */}
        <div className="bg-white rounded-lg p-4 mb-6">
          <div className="flex space-x-1">
            {statusTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleStatusFilter(tab.key)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  filters.status === tab.key ||
                  (filters.status === "" && tab.key === "all")
                    ? "bg-[#e53e3e] text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg p-4 mb-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <MkdInputV2
                placeholder="Search your listings..."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              >
                <MkdInputV2.Field
                  onKeyPress={(e: any) => e.key === "Enter" && handleSearch()}
                />
              </MkdInputV2>
            </div>
            <InteractiveButton
              onClick={handleSearch}
              className="bg-[#e53e3e] text-white px-6 py-2 rounded-md hover:bg-[#c53030]"
            >
              Search
            </InteractiveButton>
          </div>
        </div>

        {/* Listings Table */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <MkdLoader />
          </div>
        ) : (
          <>
            <div className="bg-white rounded-lg overflow-hidden mb-6">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Listing
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {listings.map((listing) => (
                      <tr key={listing.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-12 w-12 flex-shrink-0">
                              {listing.image ? (
                                <img
                                  className="h-12 w-12 rounded-lg object-cover"
                                  src={listing.image}
                                  alt={listing.name}
                                />
                              ) : (
                                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                  <span className="text-gray-400 text-xs">
                                    No Image
                                  </span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {listing.name}
                              </div>
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {listing.description}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            ${listing.price}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              listing.status === "active"
                                ? "bg-green-100 text-green-800"
                                : listing.status === "sold"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-gray-100 text-gray-800"
                            }`}
                          >
                            {listing.status.charAt(0).toUpperCase() +
                              listing.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(listing.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <Link
                              to={`/user/listings/view/${listing.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              View
                            </Link>
                            <Link
                              to={`/user/listings/edit/${listing.id}`}
                              className="text-yellow-600 hover:text-yellow-900"
                            >
                              <EditIcon className="w-4 h-4" />
                            </Link>
                            <button
                              onClick={() => handleDeleteListing(listing.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {listings.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-lg mb-2">
                    No listings found
                  </div>
                  <p className="text-gray-500 mb-4">
                    Create your first listing to get started
                  </p>
                  <Link
                    to="/user/listings/add"
                    className="bg-[#e53e3e] text-white px-4 py-2 rounded-md hover:bg-[#c53030]"
                  >
                    Add New Listing
                  </Link>
                </div>
              )}
            </div>

            {pagination && pagination.num_pages > 1 && (
              <div className="flex justify-center">
                <PaginationBar
                  currentPage={pagination.page}
                  pageCount={pagination.num_pages}
                  updateCurrentPage={handlePageChange}
                  pageSize={filters.limit}
                  canPreviousPage={pagination.has_prev}
                  canNextPage={pagination.has_next}
                  updatePageSize={() => {}}
                  startSize={1}
                  multiplier={1}
                  canChangeLimit={false}
                />
              </div>
            )}
          </>
        )}
      </div>
    </UserWrapper>
  );
};

export default UserListingsListPage;
